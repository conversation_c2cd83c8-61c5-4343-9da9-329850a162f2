nohup: ignoring input
INFO:     Started server process [78198]
INFO:     Waiting for application startup.
2025-08-03 18:17:08,948 - app.main - INFO - Initializing Entity Extraction API
2025-08-03 18:17:09,368 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 18:17:09,368 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 18:17:09,368 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,368 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,482 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 18:17:09,482 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 18:17:09,482 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,482 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,532 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 18:17:09,532 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 18:17:09,532 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,532 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,652 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:09,652 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:09,652 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:17:09,652 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:17:09,652 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,652 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,705 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:17:09,705 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:17:09,706 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,706 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,774 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:17:09,774 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:17:09,774 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,774 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,829 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:17:09,829 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:17:09,830 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,830 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,893 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:17:09,893 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:17:09,893 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,893 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:09,969 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:17:09,969 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:17:09,969 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:09,969 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,038 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:17:10,038 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:17:10,038 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,038 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,101 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:10,101 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:10,220 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 18:17:10,276 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:10,276 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:10,276 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:17:10,276 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:17:10,276 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,276 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,333 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:17:10,333 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:17:10,333 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,333 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,388 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:17:10,388 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:17:10,389 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,389 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,453 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:17:10,453 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:17:10,453 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,453 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,514 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:17:10,514 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:17:10,514 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,514 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,571 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:17:10,571 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:17:10,571 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,571 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,628 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:17:10,628 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:17:10,629 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:17:10,629 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:17:10,693 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:10,693 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:10,800 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
[2025-08-03 18:17:14][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": "2"
}
2025-08-03 18:17:14,456 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:14,456 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:14,458 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:14,458 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:14,458 INFO sqlalchemy.engine.Engine [generated in 0.00028s] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:14.383378', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:17:14.383365", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:14,458 - sqlalchemy.engine.Engine - INFO - [generated in 0.00028s] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:14.383378', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:17:14.383365", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:14,518 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:14,518 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:14,692 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:14,692 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:14,696 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:17:14,696 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:17:14,696 INFO sqlalchemy.engine.Engine [generated in 0.00022s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 18:17:14,696 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 18:17:14][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 175, status: COMPLETED
2025-08-03 18:17:15,118 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:15,118 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:15,119 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:15,119 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:15,119 INFO sqlalchemy.engine.Engine [cached since 0.6607s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:14.773790', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 175, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:14.773779", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:15,119 - sqlalchemy.engine.Engine - INFO - [cached since 0.6607s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:14.773790', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 175, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:14.773779", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:15,172 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:15,172 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:15,282 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:15,282 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:17:15,441 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:15,441 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:15,441 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:17:15,441 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:17:15,441 INFO sqlalchemy.engine.Engine [cached since 0.7455s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 18:17:15,441 - sqlalchemy.engine.Engine - INFO - [cached since 0.7455s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 18:17:15][EntityExtractor][orchestrator_************************************][************************************] WARNING: Found existing analysis during create - returning existing ID: 175
2025-08-03 18:17:15,568 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:15,568 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:15,568 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:15,568 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:15,568 INFO sqlalchemy.engine.Engine [cached since 1.11s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:15.513729', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 175", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:15.513721", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:15,568 - sqlalchemy.engine.Engine - INFO - [cached since 1.11s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:15.513729', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 175", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:15.513721", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:15,632 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:15,632 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:15,746 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:15,746 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:17:15][EntityExtractor][175][************************************] INFO: Updated logger with analysis ID: 175
2025-08-03 18:17:15,901 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:15,901 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:15,902 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:15,902 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:15,902 INFO sqlalchemy.engine.Engine [cached since 1.444s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:15.852056', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 175", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:15.852040", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:15,902 - sqlalchemy.engine.Engine - INFO - [cached since 1.444s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:15.852056', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 175", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:15.852040", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:15,965 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:15,965 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:16,124 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:16,124 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:16,126 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 18:17:16,126 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 18:17:16,126 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'pk_1': 175}
2025-08-03 18:17:16,126 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] {'pk_1': 175}
2025-08-03 18:17:16,193 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 18:17:16,193 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 18:17:16,193 INFO sqlalchemy.engine.Engine [generated in 0.00022s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T18:17:16.070025', 'entity_extraction_analysis_id': 175}
2025-08-03 18:17:16,193 - sqlalchemy.engine.Engine - INFO - [generated in 0.00022s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T18:17:16.070025', 'entity_extraction_analysis_id': 175}
2025-08-03 18:17:16,253 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:16,253 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:16][EntityExtractor][175][************************************] INFO: Updated analysis 175 status to IN_PROGRESS
2025-08-03 18:17:16,412 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:16,412 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:16,412 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:16,412 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:16,413 INFO sqlalchemy.engine.Engine [cached since 1.955s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:16.361926', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 175 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:16.361916", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:16,413 - sqlalchemy.engine.Engine - INFO - [cached since 1.955s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:16.361926', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 175 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:16.361916", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:16,476 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:16,476 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:16][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieving policy URLs with reachability status
2025-08-03 18:17:16,638 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:16,638 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:16,639 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:16,639 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:16,639 INFO sqlalchemy.engine.Engine [cached since 2.181s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:16.583126', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:16.583117", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:16,639 - sqlalchemy.engine.Engine - INFO - [cached since 2.181s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:16.583126', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:16.583117", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:16,701 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:16,701 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:17][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 18:17:17,792 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:17,792 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:17,793 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:17,793 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:17,793 INFO sqlalchemy.engine.Engine [cached since 3.335s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:17.741371', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:17.741364", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:17,793 - sqlalchemy.engine.Engine - INFO - [cached since 3.335s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:17.741371', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:17.741364", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:17,853 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:17,853 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:18][EntityExtractor][url_retrieval_************************************][************************************] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-08-03 18:17:18,303 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:18,303 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:18,304 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:18,304 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:18,304 INFO sqlalchemy.engine.Engine [cached since 3.846s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:18.255759', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:18.255750", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:18,304 - sqlalchemy.engine.Engine - INFO - [cached since 3.846s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:18.255759', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:18.255750", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:18,361 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:18,361 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:18][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 18:17:18,735 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:18,735 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:18,735 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:18,735 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:18,735 INFO sqlalchemy.engine.Engine [cached since 4.277s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:18.683082', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:18.683075", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:18,735 - sqlalchemy.engine.Engine - INFO - [cached since 4.277s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:18.683082', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:18.683075", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:18,803 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:18,803 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:19][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 18:17:19,258 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:19,258 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:19,259 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:19,259 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:19,259 INFO sqlalchemy.engine.Engine [cached since 4.801s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:19.201793', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:19.201784", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:19,259 - sqlalchemy.engine.Engine - INFO - [cached since 4.801s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:19.201793', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:19.201784", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:19,321 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:19,321 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:19][EntityExtractor][url_retrieval_************************************][************************************] INFO: Force-loaded 5 policies from policy_analysis_new_gemini as unreachable
2025-08-03 18:17:19,582 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:19,582 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:19,582 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:19,582 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:19,582 INFO sqlalchemy.engine.Engine [cached since 5.125s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:19.535811', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:19.535802", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:19,582 - sqlalchemy.engine.Engine - INFO - [cached since 5.125s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:19.535811', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:19.535802", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:19,652 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:19,652 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:19][EntityExtractor][url_retrieval_************************************][************************************] INFO: Total policy URLs retrieved: 5
2025-08-03 18:17:19,801 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:19,801 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:19,802 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:19,802 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:19,802 INFO sqlalchemy.engine.Engine [cached since 5.344s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:19.752791', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:19.752784", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:19,802 - sqlalchemy.engine.Engine - INFO - [cached since 5.344s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:19.752791', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:19.752784", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:19,861 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:19,861 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:20][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 18:17:20,242 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:20,242 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:20,242 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:20,242 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:20,243 INFO sqlalchemy.engine.Engine [cached since 5.785s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:20.185261', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:20.185253", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:20,243 - sqlalchemy.engine.Engine - INFO - [cached since 5.785s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:20.185261', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:20.185253", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:20,303 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:20,303 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:20][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 18:17:20,706 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:20,706 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:20,706 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:20,706 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:20,706 INFO sqlalchemy.engine.Engine [cached since 6.248s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:20.651915', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:20.651906", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:20,706 - sqlalchemy.engine.Engine - INFO - [cached since 6.248s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:20.651915', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:20.651906", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:20,763 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:20,763 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:20][EntityExtractor][175][************************************] INFO: Filtered policy URLs keys: ['privacy_policy', 'terms_and_condition', 'shipping_delivery', 'contact_us', 'about_us']
2025-08-03 18:17:21,034 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:21,034 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:21,035 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,035 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,035 INFO sqlalchemy.engine.Engine [cached since 6.577s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:20.975750', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:20.975741", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,035 - sqlalchemy.engine.Engine - INFO - [cached since 6.577s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:20.975750', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:20.975741", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,094 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:21,094 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:21][EntityExtractor][175][************************************] INFO: Extracted text length for privacy_policy: 2340
2025-08-03 18:17:21,252 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:21,252 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:21,253 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,253 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,253 INFO sqlalchemy.engine.Engine [cached since 6.795s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:21.202867', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.202853", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,253 - sqlalchemy.engine.Engine - INFO - [cached since 6.795s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:21.202867', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.202853", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,313 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:21,313 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:21][EntityExtractor][175][************************************] INFO: Extracted text length for terms_and_condition: 1479
2025-08-03 18:17:21,462 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:21,462 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:21,463 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,463 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,463 INFO sqlalchemy.engine.Engine [cached since 7.005s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:21.413050', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.413043", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,463 - sqlalchemy.engine.Engine - INFO - [cached since 7.005s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:21.413050', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.413043", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,524 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:21,524 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:21][EntityExtractor][url_retrieval_************************************][************************************] INFO: Checking Gemini reachability for 5 URLs
2025-08-03 18:17:21,670 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:21,670 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:21,671 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,671 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,671 INFO sqlalchemy.engine.Engine [cached since 7.213s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:21.622889', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.622882", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,671 - sqlalchemy.engine.Engine - INFO - [cached since 7.213s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:21.622889', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.622882", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,732 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:21,732 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:21][EntityExtractor][url_retrieval_************************************][************************************] INFO: Gemini reachability check completed: 5 reachable, 0 unreachable
2025-08-03 18:17:21,902 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:21,902 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:21,903 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,903 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:21,903 INFO sqlalchemy.engine.Engine [cached since 7.445s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:21.849780', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.849772", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,903 - sqlalchemy.engine.Engine - INFO - [cached since 7.445s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:21.849780', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:21.849772", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:21,963 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:21,963 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:24,136 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-08-03 18:17:41,777 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-08-03 18:17:41,778 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-08-03 18:17:24][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5395,
  "context": {
    "task_type": "legacy"
  }
}
[2025-08-03 18:17:24][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-08-03 18:17:41][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=385 candidates_tokens_details=None prompt_token_count=1281 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1281
)] thoughts_token_count=2012 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3678 traffic_type=None
[2025-08-03 18:17:41][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 842,
  "finish_reason": "STOP"
}
[2025-08-03 18:17:43][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: None
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\n        \"<EMAIL>\"\n    ],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\n        \"044-3099 1103\",\n        \"044-4344 2650\",\n        \"+91 44 46945101\"\n    ],\n    \"business_location\": [\n        \"2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096\",\n        \"Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai \u2013 600100 India\"\n    ],\n    \"accepts_international_orders\": \"not_mentioned\",\n    \"shipping_countries\": [],\n    \"shipping_policy_details\": \"\",\n    \"has_jurisdiction_law\": \"yes\",\n    \"jurisdiction_place\": [\n        \"India\"\n    ],\n    \"jurisdiction_details\": \"Terms and conditions apply to this website, operated by an Indian company. [2]\"\n}\n```"
}
[2025-08-03 18:17:43][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: ************************************
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\n        \"<EMAIL>\"\n    ],\n    \"support_email\": [],\n    \"business_contact_numbers\": [\n        \"044-3099 1103\",\n        \"044-4344 2650\",\n        \"+91 44 46945101\"\n    ],\n    \"business_location\": [\n        \"2nd Floor, Campus 4A, RMZ Millenia Business Park, 143, Dr MGR Road, Perungudi, Chennai - 600 096\",\n        \"Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai \u2013 600100 India\"\n    ],\n    \"accepts_international_orders\": \"not_mentioned\",\n    \"shipping_countries\": [],\n    \"shipping_policy_details\": \"\",\n    \"has_jurisdiction_law\": \"yes\",\n    \"jurisdiction_place\": [\n        \"India\"\n    ],\n    \"jurisdiction_details\": \"Terms and conditions apply to this website, operated by an Indian company. [2]\"\n}\n```"
}
2025-08-03 18:17:43,850 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:43,850 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:43,852 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:43,852 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:43,852 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/privacy.html'}
2025-08-03 18:17:43,852 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/privacy.html'}
[2025-08-03 18:17:43][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/privacy.html
2025-08-03 18:17:43,973 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:43,973 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:43,974 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:43,974 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:43,974 INFO sqlalchemy.engine.Engine [cached since 29.52s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:43.915232', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:43.915226", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:43,974 - sqlalchemy.engine.Engine - INFO - [cached since 29.52s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:43.915232', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:43.915226", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:44,041 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:44,041 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:44,139 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:44,139 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:17:44,321 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:44,321 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:44,321 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:44,321 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:44,321 INFO sqlalchemy.engine.Engine [cached since 0.4693s ago] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
2025-08-03 18:17:44,321 - sqlalchemy.engine.Engine - INFO - [cached since 0.4693s ago] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
[2025-08-03 18:17:44][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html
2025-08-03 18:17:44,457 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:44,457 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:44,457 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:44,457 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:44,458 INFO sqlalchemy.engine.Engine [cached since 30s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:44.387987', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:44.387982", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:44,458 - sqlalchemy.engine.Engine - INFO - [cached since 30s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:44.387987', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:44.387982", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:44,521 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:44,521 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:44,632 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:44,632 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:17:44,776 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:44,776 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:44,777 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:44,777 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:44,777 INFO sqlalchemy.engine.Engine [cached since 0.9248s ago] {'analysis_id_1': 175, 'url_1': 'not_found'}
2025-08-03 18:17:44,777 - sqlalchemy.engine.Engine - INFO - [cached since 0.9248s ago] {'analysis_id_1': 175, 'url_1': 'not_found'}
[2025-08-03 18:17:44][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: not_found
2025-08-03 18:17:44,901 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:44,901 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:44,901 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:44,901 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:44,901 INFO sqlalchemy.engine.Engine [cached since 30.44s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:44.845357', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:44.845351", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:44,901 - sqlalchemy.engine.Engine - INFO - [cached since 30.44s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:44.845357', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:44.845351", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:44,963 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:44,963 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:45,077 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:45,077 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:17:45,251 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:45,251 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:45,252 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:45,252 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:45,252 INFO sqlalchemy.engine.Engine [cached since 1.4s ago] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
2025-08-03 18:17:45,252 - sqlalchemy.engine.Engine - INFO - [cached since 1.4s ago] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
[2025-08-03 18:17:45][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html
2025-08-03 18:17:45,361 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:45,361 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:45,361 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:45,361 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:45,361 INFO sqlalchemy.engine.Engine [cached since 30.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:45.310184', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:45.310178", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:45,361 - sqlalchemy.engine.Engine - INFO - [cached since 30.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:45.310184', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:45.310178", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:45,419 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:45,419 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:45,529 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:45,529 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:17:45,692 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:45,692 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:45,692 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:45,692 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:17:45,692 INFO sqlalchemy.engine.Engine [cached since 1.84s ago] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/about-us/careers.html'}
2025-08-03 18:17:45,692 - sqlalchemy.engine.Engine - INFO - [cached since 1.84s ago] {'analysis_id_1': 175, 'url_1': 'https://www.shell.in/about-us/careers.html'}
[2025-08-03 18:17:45][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/careers.html
2025-08-03 18:17:45,815 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:45,815 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:45,815 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:45,815 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:45,815 INFO sqlalchemy.engine.Engine [cached since 31.36s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:45.750211', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:45.750205", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:45,815 - sqlalchemy.engine.Engine - INFO - [cached since 31.36s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:17:45.750211', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:45.750205", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:45,877 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:45,877 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:45,980 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:45,980 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:17:46][EntityExtractor][175][************************************] INFO: Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields
2025-08-03 18:17:46,142 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:46,142 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:46,143 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:46,143 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:46,143 INFO sqlalchemy.engine.Engine [cached since 31.68s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.086753', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.086746", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:46,143 - sqlalchemy.engine.Engine - INFO - [cached since 31.68s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.086753', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 72.7% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.086746", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:46,211 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:46,211 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:46][EntityExtractor][175][************************************] INFO: Gemini missing fields to fallback: ['support_email', 'shipping_countries', 'shipping_policy_details']
2025-08-03 18:17:46,361 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:46,361 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:46,362 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:46,362 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:46,362 INFO sqlalchemy.engine.Engine [cached since 31.9s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.312874', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.312854", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:46,362 - sqlalchemy.engine.Engine - INFO - [cached since 31.9s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.312874', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.312854", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:46,415 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:46,415 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:46][EntityExtractor][175][************************************] INFO: Starting OpenAI backup flow for missing fields: ['support_email', 'shipping_countries', 'shipping_policy_details']
2025-08-03 18:17:46,595 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:46,595 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:46,595 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:46,595 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:46,595 INFO sqlalchemy.engine.Engine [cached since 32.14s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.521623', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.521614", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:46,595 - sqlalchemy.engine.Engine - INFO - [cached since 32.14s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.521623', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'support_email\', \'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.521614", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:46,658 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:46,658 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:46,827 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:46,827 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:46,829 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 18:17:46,829 - sqlalchemy.engine.Engine - INFO - SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 18:17:46,829 INFO sqlalchemy.engine.Engine [generated in 0.00015s] {'scrape_request_ref_id_1': '************************************'}
2025-08-03 18:17:46,829 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] {'scrape_request_ref_id_1': '************************************'}
[2025-08-03 18:17:46][EntityExtractor][175][************************************] INFO: Retrieved policy texts: ['privacy_policy', 'terms_and_condition', 'contact_us', 'about_us']
2025-08-03 18:17:47,016 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:47,016 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:47,016 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:47,016 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:47,016 INFO sqlalchemy.engine.Engine [cached since 32.56s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.963844', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.963837", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:47,016 - sqlalchemy.engine.Engine - INFO - [cached since 32.56s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:46.963844', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:46.963837", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:47,072 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:47,072 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:47,209 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:17:47,209 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:17:47][EntityExtractor][175][************************************] INFO: Found policy texts for 4 policy types
2025-08-03 18:17:47,404 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:47,404 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:47,405 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:47,405 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:47,405 INFO sqlalchemy.engine.Engine [cached since 32.95s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:47.341272', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:47.341264", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:47,405 - sqlalchemy.engine.Engine - INFO - [cached since 32.95s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:47.341272', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:47.341264", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:47,460 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:47,460 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:47][EntityExtractor][175][************************************] INFO: Processing contact_info with fields: ['support_email']
2025-08-03 18:17:47,766 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:47,766 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:47,767 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:47,767 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:47,767 INFO sqlalchemy.engine.Engine [cached since 33.31s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:47.719115', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:47.719106", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:47,767 - sqlalchemy.engine.Engine - INFO - [cached since 33.31s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:47.719115', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing contact_info with fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:47.719106", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:47,832 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:47,832 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:49,162 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 18:17:49,220 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:49,220 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:49,220 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:49,220 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:49,220 INFO sqlalchemy.engine.Engine [cached since 34.76s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:17:47', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:17:49,220 - sqlalchemy.engine.Engine - INFO - [cached since 34.76s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:17:47', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (10420 characters truncated) ... \\n{\\n    \\"support_email\\": \\"extracted support/customer email or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"support_email\\": \\"<EMAIL>\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:17:49,276 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:49,276 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:49][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: None
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}"
}
[2025-08-03 18:17:49][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_contact_info, Request ID: ************************************
{
  "response_preview": "{\n    \"support_email\": \"<EMAIL>\"\n}"
}
[2025-08-03 18:17:49][EntityExtractor][175][************************************] INFO: OpenAI contact_info call extracted 1 fields: ['support_email']
2025-08-03 18:17:49,469 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:49,469 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:49,469 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:49,469 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:49,469 INFO sqlalchemy.engine.Engine [cached since 35.01s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:49.401313', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:49.401307", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:49,469 - sqlalchemy.engine.Engine - INFO - [cached since 35.01s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:49.401313', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI contact_info call extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:49.401307", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:49,532 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:49,532 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:49][EntityExtractor][175][************************************] INFO: Processing shipping_info with fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 18:17:49,709 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:49,709 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:49,709 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:49,709 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:49,710 INFO sqlalchemy.engine.Engine [cached since 35.25s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:49.646870', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:49.646855", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:49,710 - sqlalchemy.engine.Engine - INFO - [cached since 35.25s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:49.646870', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:49.646855", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:49,767 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:49,767 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:50,923 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 18:17:50,981 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:50,981 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:50,981 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:50,981 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:50,981 INFO sqlalchemy.engine.Engine [cached since 36.52s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:17:49', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:17:50,981 - sqlalchemy.engine.Engine - INFO - [cached since 36.52s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:17:49', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:17:51,065 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:51,065 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:51][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:17:51][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:17:51][EntityExtractor][175][************************************] INFO: OpenAI shipping_info call extracted 0 fields: []
2025-08-03 18:17:51,244 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:51,244 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:51,245 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:51,245 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:51,245 INFO sqlalchemy.engine.Engine [cached since 36.79s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:51.181631', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:51.181626", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:51,245 - sqlalchemy.engine.Engine - INFO - [cached since 36.79s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:51.181631', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:51.181626", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:51,307 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:51,307 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:51][EntityExtractor][175][************************************] INFO: Making final comprehensive call for remaining fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 18:17:51,478 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:51,478 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:51,478 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:51,478 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:51,479 INFO sqlalchemy.engine.Engine [cached since 37.02s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:51.421909', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:51.421900", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:51,479 - sqlalchemy.engine.Engine - INFO - [cached since 37.02s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:51.421909', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:51.421900", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:51,540 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:51,540 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:17:52,852 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 18:17:52,916 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:52,916 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:52,917 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:52,917 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:52,917 INFO sqlalchemy.engine.Engine [cached since 38.46s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:17:51', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:17:52,917 - sqlalchemy.engine.Engine - INFO - [cached since 38.46s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:17:51', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:17:53,056 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:53,056 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:53][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:17:53][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:17:53][EntityExtractor][175][************************************] INFO: OpenAI comprehensive call extracted 0 fields: []
2025-08-03 18:17:53,249 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:53,249 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:53,249 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,249 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,249 INFO sqlalchemy.engine.Engine [cached since 38.79s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.185943', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.185938", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:53,249 - sqlalchemy.engine.Engine - INFO - [cached since 38.79s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.185943', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.185938", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:53,324 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:53,324 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:53][EntityExtractor][175][************************************] INFO: Total OpenAI backup extracted 1 fields: ['support_email']
2025-08-03 18:17:53,464 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:53,464 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:53,465 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,465 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,465 INFO sqlalchemy.engine.Engine [cached since 39.01s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.417084', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.417077", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:53,465 - sqlalchemy.engine.Engine - INFO - [cached since 39.01s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.417084', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 1 fields: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.417077", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:53,525 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:53,525 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:53][EntityExtractor][175][************************************] INFO: OpenAI backup extracted: ['support_email']
2025-08-03 18:17:53,685 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:53,685 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:53,686 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,686 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,686 INFO sqlalchemy.engine.Engine [cached since 39.23s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.629921', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.629912", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:53,686 - sqlalchemy.engine.Engine - INFO - [cached since 39.23s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.629921', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup extracted: [\'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.629912", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:53,748 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:53,748 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:53][EntityExtractor][175][************************************] INFO: Merging 2 AI extraction results
2025-08-03 18:17:53,932 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:53,932 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:53,933 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,933 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:53,933 INFO sqlalchemy.engine.Engine [cached since 39.47s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.874166', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.874155", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:53,933 - sqlalchemy.engine.Engine - INFO - [cached since 39.47s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:53.874166', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 2 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:53.874155", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,001 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:54,001 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:54][EntityExtractor][175][************************************] INFO: Merged result contains 8 fields
2025-08-03 18:17:54,204 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:54,204 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:54,205 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,205 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,205 INFO sqlalchemy.engine.Engine [cached since 39.75s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.144580', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.144569", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,205 - sqlalchemy.engine.Engine - INFO - [cached since 39.75s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.144580', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.144569", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,262 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:54,262 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:54][EntityExtractor][175][************************************] INFO: Final merged extraction result keys: ['legal_name', 'business_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'support_email']
2025-08-03 18:17:54,427 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:54,427 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:54,427 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,427 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,427 INFO sqlalchemy.engine.Engine [cached since 39.97s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.371921', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (75 characters truncated) ... tails\', \'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.371910", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,427 - sqlalchemy.engine.Engine - INFO - [cached since 39.97s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.371921', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'business_contact_numbers\', \'business_loca ... (75 characters truncated) ... tails\', \'support_email\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.371910", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,492 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:54,492 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:54][EntityExtractor][175][************************************] INFO: Fallback raw text for privacy_policy_text: present
2025-08-03 18:17:54,694 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:54,694 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:54,694 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,694 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,694 INFO sqlalchemy.engine.Engine [cached since 40.24s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.627012', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.627004", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,694 - sqlalchemy.engine.Engine - INFO - [cached since 40.24s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.627012', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.627004", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,761 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:54,761 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:54][EntityExtractor][175][************************************] INFO: After fallback, merged_result[privacy_policy_text] length: 2340
2025-08-03 18:17:54,943 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:54,943 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:54,943 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,943 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:54,943 INFO sqlalchemy.engine.Engine [cached since 40.49s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.882906', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.882898", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:54,943 - sqlalchemy.engine.Engine - INFO - [cached since 40.49s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:54.882906', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:54.882898", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:55,001 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:55,001 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:55][EntityExtractor][175][************************************] INFO: Fallback raw text for terms_conditions_text: present
2025-08-03 18:17:55,176 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:55,176 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:55,177 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:55,177 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:55,177 INFO sqlalchemy.engine.Engine [cached since 40.72s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:55.132966', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:55.132957", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:55,177 - sqlalchemy.engine.Engine - INFO - [cached since 40.72s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:55.132966', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:55.132957", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:55,235 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:55,235 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:55][EntityExtractor][175][************************************] INFO: After fallback, merged_result[terms_conditions_text] length: 1479
2025-08-03 18:17:55,433 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:55,433 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:55,433 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:55,433 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:55,433 INFO sqlalchemy.engine.Engine [cached since 40.98s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:55.365471', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:55.365461", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:55,433 - sqlalchemy.engine.Engine - INFO - [cached since 40.98s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:55.365471', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:55.365461", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:55,492 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:55,492 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:17:55][EntityExtractor][175][************************************] INFO: Before database storage - jurisdiction_details: Terms and conditions apply to this website, operated by an Indian company. [2]
2025-08-03 18:17:55,708 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:17:55,708 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:17:55,709 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:55,709 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:17:55,709 INFO sqlalchemy.engine.Engine [cached since 41.25s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:55.650248', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: Terms and conditions apply to this website, operated by an Indian company. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:55.650234", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:55,709 - sqlalchemy.engine.Engine - INFO - [cached since 41.25s ago] {'analysis_id': 175, 'timestamp': '2025-08-03T18:17:55.650248', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: Terms and conditions apply to this website, operated by an Indian company. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:17:55.650234", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:17:55,773 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:17:55,773 - sqlalchemy.engine.Engine - INFO - COMMIT
