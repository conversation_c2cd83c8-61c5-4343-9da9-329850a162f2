nohup: ignoring input
INFO:     Started server process [76719]
INFO:     Waiting for application startup.
2025-08-03 18:00:45,504 - app.main - INFO - Initializing Entity Extraction API
2025-08-03 18:00:45,883 INFO sqlalchemy.engine.Engine SELECT DATABASE()
2025-08-03 18:00:45,883 - sqlalchemy.engine.Engine - INFO - SELECT DATABASE()
2025-08-03 18:00:45,883 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:45,883 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:45,983 INFO sqlalchemy.engine.Engine SELECT @@sql_mode
2025-08-03 18:00:45,983 - sqlalchemy.engine.Engine - INFO - SELECT @@sql_mode
2025-08-03 18:00:45,983 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:45,983 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,039 INFO sqlalchemy.engine.Engine SELECT @@lower_case_table_names
2025-08-03 18:00:46,039 - sqlalchemy.engine.Engine - INFO - SELECT @@lower_case_table_names
2025-08-03 18:00:46,040 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,040 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,157 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:00:46,157 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:00:46,161 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:00:46,161 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:00:46,161 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,161 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,214 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:00:46,214 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:00:46,214 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,214 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,267 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:00:46,267 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:00:46,267 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,267 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,349 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:00:46,349 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:00:46,349 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,349 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,427 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:00:46,427 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:00:46,427 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,427 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,503 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:00:46,503 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:00:46,503 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,503 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,556 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:00:46,556 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:00:46,557 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,557 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,625 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:00:46,625 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:00:46,732 - app.main - INFO - Database initialized successfully
Creating Entity Extractor database tables...
2025-08-03 18:00:46,792 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:00:46,792 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:00:46,793 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:00:46,793 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`website_urls_gemini`
2025-08-03 18:00:46,793 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,793 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,856 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:00:46,856 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`general_logs_gemini`
2025-08-03 18:00:46,856 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,856 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,920 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:00:46,920 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`mcc_url_classification_gemini`
2025-08-03 18:00:46,920 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,920 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:46,976 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:00:46,976 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_url_classification_gemini`
2025-08-03 18:00:46,976 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:46,976 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:47,035 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:00:47,035 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`policy_analysis_new_gemini`
2025-08-03 18:00:47,035 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:47,035 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:47,097 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:00:47,097 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_analysis`
2025-08-03 18:00:47,097 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:47,097 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:47,167 INFO sqlalchemy.engine.Engine DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:00:47,167 - sqlalchemy.engine.Engine - INFO - DESCRIBE `ds-api-db`.`entity_extraction_url_analysis`
2025-08-03 18:00:47,167 INFO sqlalchemy.engine.Engine [raw sql] {}
2025-08-03 18:00:47,167 - sqlalchemy.engine.Engine - INFO - [raw sql] {}
2025-08-03 18:00:47,233 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:00:47,233 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:00:47,337 - app.main - INFO - Entity Extractor tables initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
✅ Entity Extractor database tables created successfully!
Tables created:
  - entity_extraction_analysis
  - entity_extraction_url_analysis
[2025-08-03 18:01:21][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": "2"
}
2025-08-03 18:01:21,123 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:01:21,123 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:01:21,125 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:01:21,125 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:01:21,125 INFO sqlalchemy.engine.Engine [generated in 0.00019s] {'analysis_id': 0, 'timestamp': '2025-08-03T18:01:21.045643', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:01:21.045634", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:01:21,125 - sqlalchemy.engine.Engine - INFO - [generated in 0.00019s] {'analysis_id': 0, 'timestamp': '2025-08-03T18:01:21.045643', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:01:21.045634", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:01:21,193 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:01:21,193 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:01:21,413 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:01:21,413 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:01:21,416 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:01:21,416 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:01:21,416 INFO sqlalchemy.engine.Engine [generated in 0.00020s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 18:01:21,416 - sqlalchemy.engine.Engine - INFO - [generated in 0.00020s] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 18:01:21][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 174, status: COMPLETED
2025-08-03 18:01:21,863 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:01:21,863 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:01:21,863 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:01:21,863 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:01:21,864 INFO sqlalchemy.engine.Engine [cached since 0.7387s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:01:21.490513', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 174, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:01:21.490503", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:01:21,864 - sqlalchemy.engine.Engine - INFO - [cached since 0.7387s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:01:21.490513', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 174, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:01:21.490503", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:01:21,938 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:01:21,938 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:01:22,059 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:01:22,059 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:01:22][EntityExtractor][orchestrator_************************************][************************************] INFO: Returning existing analysis: 174
2025-08-03 18:01:22,227 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:01:22,227 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:01:22,228 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:01:22,228 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:01:22,228 INFO sqlalchemy.engine.Engine [cached since 1.103s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:01:22.165770', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Returning existing analysis: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:01:22.165761", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:01:22,228 - sqlalchemy.engine.Engine - INFO - [cached since 1.103s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:01:22.165770', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Returning existing analysis: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:01:22.165761", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:01:22,298 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:01:22,298 - sqlalchemy.engine.Engine - INFO - COMMIT
INFO:     127.0.0.1:35580 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
[2025-08-03 18:02:33][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": "2"
}
2025-08-03 18:02:33,582 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:02:33,582 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:02:33,583 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:02:33,583 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:02:33,583 INFO sqlalchemy.engine.Engine [cached since 72.46s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:02:33.532157', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:02:33.532150", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:02:33,583 - sqlalchemy.engine.Engine - INFO - [cached since 72.46s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:02:33.532157', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:02:33.532150", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:02:33,642 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:02:33,642 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:02:33,806 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:02:33,806 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:02:33,807 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:02:33,807 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:02:33,807 INFO sqlalchemy.engine.Engine [cached since 72.39s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 18:02:33,807 - sqlalchemy.engine.Engine - INFO - [cached since 72.39s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 18:02:33][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 174, status: COMPLETED
2025-08-03 18:02:33,943 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:02:33,943 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:02:33,943 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:02:33,943 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:02:33,943 INFO sqlalchemy.engine.Engine [cached since 72.82s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:02:33.883409', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 174, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:02:33.883401", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:02:33,943 - sqlalchemy.engine.Engine - INFO - [cached since 72.82s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:02:33.883409', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 174, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:02:33.883401", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:02:34,012 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:02:34,012 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:02:34,108 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:02:34,108 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:02:34][EntityExtractor][orchestrator_************************************][************************************] INFO: Returning existing analysis: 174
2025-08-03 18:02:34,277 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:02:34,277 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:02:34,278 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:02:34,278 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:02:34,278 INFO sqlalchemy.engine.Engine [cached since 73.15s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:02:34.227046', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Returning existing analysis: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:02:34.227037", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:02:34,278 - sqlalchemy.engine.Engine - INFO - [cached since 73.15s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:02:34.227046', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Returning existing analysis: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:02:34.227037", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:02:34,325 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:02:34,325 - sqlalchemy.engine.Engine - INFO - COMMIT
INFO:     127.0.0.1:53678 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
[2025-08-03 18:03:20][EntityExtractor][orchestrator_************************************][************************************] INFO: Starting simplified entity extraction orchestration
{
  "scrape_request_ref_id": "************************************",
  "website_url": "https://www.shell.in",
  "org_id": "2"
}
2025-08-03 18:03:20,405 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:20,405 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:20,406 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:20,406 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:20,406 INFO sqlalchemy.engine.Engine [cached since 119.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:20.333973', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:03:20.333965", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:20,406 - sqlalchemy.engine.Engine - INFO - [cached since 119.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:20.333973', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting simplified entity extraction orchestration", "scrape_request_ref_id": "************************************",  ... (22 characters truncated) ... 03T18:03:20.333965", "data": {"scrape_request_ref_id": "************************************", "website_url": "https://www.shell.in", "org_id": "2"}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:20,477 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:20,477 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:20,652 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:20,652 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:20,652 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:03:20,652 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:03:20,652 INFO sqlalchemy.engine.Engine [cached since 119.2s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 18:03:20,652 - sqlalchemy.engine.Engine - INFO - [cached since 119.2s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 18:03:20][EntityExtractor][orchestrator_************************************][************************************] INFO: Found existing analysis with ID: 174, status: COMPLETED
2025-08-03 18:03:20,788 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:20,788 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:20,788 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:20,788 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:20,788 INFO sqlalchemy.engine.Engine [cached since 119.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:20.722596', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 174, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:20.722588", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:20,788 - sqlalchemy.engine.Engine - INFO - [cached since 119.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:20.722596', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found existing analysis with ID: 174, status: COMPLETED", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:20.722588", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:20,857 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:20,857 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:20,967 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:20,967 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:03:21,137 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:21,137 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:21,138 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:03:21,138 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.scrape_request_ref_id = %(scrape_request_ref_id_1)s AND entity_extraction_analysis.org_id = %(org_id_1)s ORDER BY entity_extraction_analysis.id DESC
2025-08-03 18:03:21,138 INFO sqlalchemy.engine.Engine [cached since 119.7s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
2025-08-03 18:03:21,138 - sqlalchemy.engine.Engine - INFO - [cached since 119.7s ago] {'scrape_request_ref_id_1': '************************************', 'org_id_1': '2'}
[2025-08-03 18:03:21][EntityExtractor][orchestrator_************************************][************************************] WARNING: Found existing analysis during create - returning existing ID: 174
2025-08-03 18:03:21,284 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:21,284 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:21,285 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:21,285 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:21,285 INFO sqlalchemy.engine.Engine [cached since 120.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:21.216544', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:21.216536", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:21,285 - sqlalchemy.engine.Engine - INFO - [cached since 120.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:21.216544', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "Found existing analysis during create - returning existing ID: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:21.216536", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:21,359 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:21,359 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:21,462 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:21,462 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:03:21][EntityExtractor][174][************************************] INFO: Updated logger with analysis ID: 174
2025-08-03 18:03:21,626 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:21,626 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:21,626 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:21,626 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:21,626 INFO sqlalchemy.engine.Engine [cached since 120.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:21.577942', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:21.577933", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:21,626 - sqlalchemy.engine.Engine - INFO - [cached since 120.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:21.577942', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated logger with analysis ID: 174", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:21.577933", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:21,685 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:21,685 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:21,901 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:21,901 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:21,903 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 18:03:21,903 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id AS entity_extraction_analysis_id, entity_extraction_analysis.scrape_request_ref_id AS entity_extraction_analysis_scrape_request_ref_id, entity_extraction_analysis.website_url AS entity_extraction_analysis_website_url, entity_extraction_analysis.processing_status AS entity_extraction_analysis_processing_status, entity_extraction_analysis.legal_name AS entity_extraction_analysis_legal_name, entity_extraction_analysis.business_email AS entity_extraction_analysis_business_email, entity_extraction_analysis.support_email AS entity_extraction_analysis_support_email, entity_extraction_analysis.business_contact_numbers AS entity_extraction_analysis_business_contact_numbers, entity_extraction_analysis.business_location AS entity_extraction_analysis_business_location, entity_extraction_analysis.has_jurisdiction_law AS entity_extraction_analysis_has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details AS entity_extraction_analysis_jurisdiction_details, entity_extraction_analysis.accepts_international_orders AS entity_extraction_analysis_accepts_international_orders, entity_extraction_analysis.shipping_policy_details AS entity_extraction_analysis_shipping_policy_details, entity_extraction_analysis.jurisdiction_place AS entity_extraction_analysis_jurisdiction_place, entity_extraction_analysis.shipping_countries AS entity_extraction_analysis_shipping_countries, entity_extraction_analysis.privacy_policy_text AS entity_extraction_analysis_privacy_policy_text, entity_extraction_analysis.terms_conditions_text AS entity_extraction_analysis_terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini AS entity_extraction_analysis_urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini AS entity_extraction_analysis_urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method AS entity_extraction_analysis_extraction_method, entity_extraction_analysis.total_urls_processed AS entity_extraction_analysis_total_urls_processed, entity_extraction_analysis.all_urls_found AS entity_extraction_analysis_all_urls_found, entity_extraction_analysis.reachable_urls AS entity_extraction_analysis_reachable_urls, entity_extraction_analysis.unreachable_urls AS entity_extraction_analysis_unreachable_urls, entity_extraction_analysis.policy_urls_matched AS entity_extraction_analysis_policy_urls_matched, entity_extraction_analysis.created_at AS entity_extraction_analysis_created_at, entity_extraction_analysis.started_at AS entity_extraction_analysis_started_at, entity_extraction_analysis.completed_at AS entity_extraction_analysis_completed_at, entity_extraction_analysis.error_message AS entity_extraction_analysis_error_message, entity_extraction_analysis.org_id AS entity_extraction_analysis_org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 18:03:21,903 INFO sqlalchemy.engine.Engine [generated in 0.00015s] {'pk_1': 174}
2025-08-03 18:03:21,903 - sqlalchemy.engine.Engine - INFO - [generated in 0.00015s] {'pk_1': 174}
2025-08-03 18:03:21,975 INFO sqlalchemy.engine.Engine UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 18:03:21,975 - sqlalchemy.engine.Engine - INFO - UPDATE entity_extraction_analysis SET processing_status=%(processing_status)s, started_at=%(started_at)s WHERE entity_extraction_analysis.id = %(entity_extraction_analysis_id)s
2025-08-03 18:03:21,975 INFO sqlalchemy.engine.Engine [generated in 0.00017s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T18:03:21.814875', 'entity_extraction_analysis_id': 174}
2025-08-03 18:03:21,975 - sqlalchemy.engine.Engine - INFO - [generated in 0.00017s] {'processing_status': 'IN_PROGRESS', 'started_at': '2025-08-03T18:03:21.814875', 'entity_extraction_analysis_id': 174}
2025-08-03 18:03:22,033 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:22,033 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:22][EntityExtractor][174][************************************] INFO: Updated analysis 174 status to IN_PROGRESS
2025-08-03 18:03:22,208 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:22,208 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:22,208 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:22,208 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:22,209 INFO sqlalchemy.engine.Engine [cached since 121.1s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:22.153559', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 174 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:22.153548", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:22,209 - sqlalchemy.engine.Engine - INFO - [cached since 121.1s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:22.153559', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Updated analysis 174 status to IN_PROGRESS", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:22.153548", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:22,273 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:22,273 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:22][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieving policy URLs with reachability status
2025-08-03 18:03:22,450 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:22,450 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:22,451 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:22,451 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:22,451 INFO sqlalchemy.engine.Engine [cached since 121.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:22.402675', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:22.402668", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:22,451 - sqlalchemy.engine.Engine - INFO - [cached since 121.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:22.402675', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieving policy URLs with reachability status", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:22.402668", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:22,545 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:22,545 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:23][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 18:03:23,730 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:23,730 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:23,730 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:23,730 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:23,730 INFO sqlalchemy.engine.Engine [cached since 122.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:23.676039', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:23.676031", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:23,730 - sqlalchemy.engine.Engine - INFO - [cached since 122.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:23.676039', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:23.676031", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:23,783 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:23,783 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:24][EntityExtractor][url_retrieval_************************************][************************************] INFO: MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini
2025-08-03 18:03:24,317 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:24,317 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:24,318 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:24,318 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:24,318 INFO sqlalchemy.engine.Engine [cached since 123.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:24.260908', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:24.260901", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:24,318 - sqlalchemy.engine.Engine - INFO - [cached since 123.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:24.260908', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "MCC only provided 0 policy types, force-loading 5 missing policies from policy_analysis_new_gemini", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:24.260901", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:24,401 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:24,401 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:24][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 18:03:24,824 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:24,824 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:24,824 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:24,824 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:24,825 INFO sqlalchemy.engine.Engine [cached since 123.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:24.773652', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:24.773644", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:24,825 - sqlalchemy.engine.Engine - INFO - [cached since 123.7s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:24.773652', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:24.773644", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:24,898 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:24,898 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:25][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 18:03:25,364 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:25,364 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:25,364 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:25,364 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:25,364 INFO sqlalchemy.engine.Engine [cached since 124.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:25.288730', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:25.288723", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:25,364 - sqlalchemy.engine.Engine - INFO - [cached since 124.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:25.288730', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:25.288723", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:25,449 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:25,449 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:25][EntityExtractor][url_retrieval_************************************][************************************] INFO: Force-loaded 5 policies from policy_analysis_new_gemini as unreachable
2025-08-03 18:03:25,767 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:25,767 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:25,768 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:25,768 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:25,768 INFO sqlalchemy.engine.Engine [cached since 124.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:25.703912', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:25.703904", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:25,768 - sqlalchemy.engine.Engine - INFO - [cached since 124.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:25.703912', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Force-loaded 5 policies from policy_analysis_new_gemini as unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:25.703904", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:25,832 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:25,832 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:25][EntityExtractor][url_retrieval_************************************][************************************] INFO: Total policy URLs retrieved: 5
2025-08-03 18:03:26,023 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:26,023 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:26,023 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:26,023 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:26,023 INFO sqlalchemy.engine.Engine [cached since 124.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:25.963813', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:25.963804", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:26,023 - sqlalchemy.engine.Engine - INFO - [cached since 124.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:25.963813', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total policy URLs retrieved: 5", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:25.963804", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:26,082 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:26,082 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:26][EntityExtractor][url_retrieval_************************************][************************************] INFO: Found latest scrape_request_ref_id: ************************************ for domain: shell.in
2025-08-03 18:03:26,432 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:26,432 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:26,433 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:26,433 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:26,433 INFO sqlalchemy.engine.Engine [cached since 125.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:26.386807', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:26.386800", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:26,433 - sqlalchemy.engine.Engine - INFO - [cached since 125.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:26.386807', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found latest scrape_request_ref_id: ************************************ for domain: shell.in", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:26.386800", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:26,494 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:26,494 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:26][EntityExtractor][url_retrieval_************************************][************************************] INFO: Retrieved policy analysis data for 6 policy types
2025-08-03 18:03:26,936 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:26,936 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:26,936 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:26,936 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:26,936 INFO sqlalchemy.engine.Engine [cached since 125.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:26.861414', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:26.861406", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:26,936 - sqlalchemy.engine.Engine - INFO - [cached since 125.8s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:26.861414', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy analysis data for 6 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:26.861406", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:27,017 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:27,017 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:27][EntityExtractor][174][************************************] INFO: Filtered policy URLs keys: ['privacy_policy', 'terms_and_condition', 'shipping_delivery', 'contact_us', 'about_us']
2025-08-03 18:03:27,388 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:27,388 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:27,389 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:27,389 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:27,389 INFO sqlalchemy.engine.Engine [cached since 126.3s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:27.336555', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:27.336547", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:27,389 - sqlalchemy.engine.Engine - INFO - [cached since 126.3s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:27.336555', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Filtered policy URLs keys: [\'privacy_policy\', \'terms_and_condition\', \'shipping_delivery\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:27.336547", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:27,474 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:27,474 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:27][EntityExtractor][174][************************************] INFO: Extracted text length for privacy_policy: 2340
2025-08-03 18:03:27,699 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:27,699 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:27,699 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:27,699 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:27,699 INFO sqlalchemy.engine.Engine [cached since 126.6s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:27.609682', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:27.609673", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:27,699 - sqlalchemy.engine.Engine - INFO - [cached since 126.6s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:27.609682', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for privacy_policy: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:27.609673", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:27,778 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:27,778 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:27][EntityExtractor][174][************************************] INFO: Extracted text length for terms_and_condition: 1479
2025-08-03 18:03:27,959 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:27,959 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:27,959 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:27,959 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:27,959 INFO sqlalchemy.engine.Engine [cached since 126.8s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:27.903990', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:27.903980", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:27,959 - sqlalchemy.engine.Engine - INFO - [cached since 126.8s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:27.903990', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Extracted text length for terms_and_condition: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:27.903980", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:28,027 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:28,027 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:28][EntityExtractor][url_retrieval_************************************][************************************] INFO: Checking Gemini reachability for 5 URLs
2025-08-03 18:03:28,195 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:28,195 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:28,195 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:28,195 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:28,195 INFO sqlalchemy.engine.Engine [cached since 127.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:28.135125', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:28.135117", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:28,195 - sqlalchemy.engine.Engine - INFO - [cached since 127.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:28.135125', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Checking Gemini reachability for 5 URLs", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:28.135117", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:28,252 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:28,252 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:28][EntityExtractor][url_retrieval_************************************][************************************] INFO: Gemini reachability check completed: 5 reachable, 0 unreachable
2025-08-03 18:03:28,430 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:28,430 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:28,430 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:28,430 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:28,430 INFO sqlalchemy.engine.Engine [cached since 127.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:28.376982', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:28.376974", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:28,430 - sqlalchemy.engine.Engine - INFO - [cached since 127.3s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:28.376982', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini reachability check completed: 5 reachable, 0 unreachable", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:28.376974", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:28,493 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:28,493 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:30,639 - google_genai.models - INFO - AFC is enabled with max remote calls: 20000.
2025-08-03 18:03:45,830 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent "HTTP/1.1 200 OK"
2025-08-03 18:03:45,831 - google_genai.models - INFO - AFC remote call 1 is done.
[2025-08-03 18:03:30][legacy_unknown][unknown] INFO: Starting Gemini API call
{
  "model": "gemini-2.5-flash",
  "timeout": 120,
  "max_retries": 3,
  "prompt_length": 5395,
  "context": {
    "task_type": "legacy"
  }
}
[2025-08-03 18:03:30][legacy_unknown][unknown] INFO: Gemini API attempt 1/3
[2025-08-03 18:03:45][legacy_unknown][unknown] INFO: Gemini API Usage: cache_tokens_details=None cached_content_token_count=None candidates_token_count=325 candidates_tokens_details=None prompt_token_count=1281 prompt_tokens_details=[ModalityTokenCount(
  modality=<MediaModality.TEXT: 'TEXT'>,
  token_count=1281
)] thoughts_token_count=1493 tool_use_prompt_token_count=None tool_use_prompt_tokens_details=None total_token_count=3099 traffic_type=None
[2025-08-03 18:03:45][legacy_unknown][unknown] INFO: Gemini API call successful
{
  "attempt": 1,
  "response_length": 710,
  "finish_reason": "STOP"
}
[2025-08-03 18:03:47][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: None
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\"<EMAIL>\"],\n    \"support_email\": [\"<EMAIL>\"],\n    \"business_contact_numbers\": [\"044-3099 1103\", \"044-4344 2650\", \"+91 44 46945101\"],\n    \"business_location\": [\"Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai \u2013 600100 India\"],\n    \"accepts_international_orders\": \"not_mentioned\",\n    \"shipping_countries\": [],\n    \"shipping_policy_details\": \"\",\n    \"has_jurisdiction_law\": \"yes\",\n    \"jurisdiction_place\": [\"India\"],\n    \"jurisdiction_details\": \"Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]\"\n}\n```"
}
[2025-08-03 18:03:47][api_response_logger][unknown] INFO: Gemini API Call - Model: gemini-2.5-flash, Request ID: ************************************
{
  "response_preview": "```json\n{\n    \"legal_name\": \"Shell India Markets Private Limited\",\n    \"business_email\": [\"<EMAIL>\"],\n    \"support_email\": [\"<EMAIL>\"],\n    \"business_contact_numbers\": [\"044-3099 1103\", \"044-4344 2650\", \"+91 44 46945101\"],\n    \"business_location\": [\"Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai \u2013 600100 India\"],\n    \"accepts_international_orders\": \"not_mentioned\",\n    \"shipping_countries\": [],\n    \"shipping_policy_details\": \"\",\n    \"has_jurisdiction_law\": \"yes\",\n    \"jurisdiction_place\": [\"India\"],\n    \"jurisdiction_details\": \"Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]\"\n}\n```"
}
2025-08-03 18:03:47,923 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:47,923 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:47,925 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:47,925 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:47,925 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/privacy.html'}
2025-08-03 18:03:47,925 - sqlalchemy.engine.Engine - INFO - [generated in 0.00018s] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/privacy.html'}
[2025-08-03 18:03:47][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/privacy.html
2025-08-03 18:03:48,068 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:48,068 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:48,069 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:48,069 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:48,069 INFO sqlalchemy.engine.Engine [cached since 146.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:47.996365', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:47.996358", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:48,069 - sqlalchemy.engine.Engine - INFO - [cached since 146.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:47.996365', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/privacy.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:47.996358", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:48,135 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:48,135 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:48,263 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:48,263 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:03:48,514 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:48,514 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:48,514 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:48,514 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:48,514 INFO sqlalchemy.engine.Engine [cached since 0.5895s ago] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
2025-08-03 18:03:48,514 - sqlalchemy.engine.Engine - INFO - [cached since 0.5895s ago] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/terms-and-conditions.html'}
[2025-08-03 18:03:48][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html
2025-08-03 18:03:48,661 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:48,661 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:48,661 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:48,661 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:48,661 INFO sqlalchemy.engine.Engine [cached since 147.5s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:48.600681', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:48.600674", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:48,661 - sqlalchemy.engine.Engine - INFO - [cached since 147.5s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:48.600681', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/terms-and-conditions.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:48.600674", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:48,715 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:48,715 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:48,857 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:48,857 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:03:49,044 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:49,044 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:49,045 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:49,045 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:49,045 INFO sqlalchemy.engine.Engine [cached since 1.12s ago] {'analysis_id_1': 174, 'url_1': 'not_found'}
2025-08-03 18:03:49,045 - sqlalchemy.engine.Engine - INFO - [cached since 1.12s ago] {'analysis_id_1': 174, 'url_1': 'not_found'}
[2025-08-03 18:03:49][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: not_found
2025-08-03 18:03:49,185 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:49,185 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:49,186 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:49,186 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:49,186 INFO sqlalchemy.engine.Engine [cached since 148.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:49.117197', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:49.117190", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:49,186 - sqlalchemy.engine.Engine - INFO - [cached since 148.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:49.117197', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: not_found", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:49.117190", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:49,245 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:49,245 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:49,372 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:49,372 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:03:49,562 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:49,562 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:49,563 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:49,563 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:49,563 INFO sqlalchemy.engine.Engine [cached since 1.638s ago] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
2025-08-03 18:03:49,563 - sqlalchemy.engine.Engine - INFO - [cached since 1.638s ago] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/about-us/contact-us.html'}
[2025-08-03 18:03:49][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html
2025-08-03 18:03:49,703 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:49,703 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:49,703 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:49,703 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:49,704 INFO sqlalchemy.engine.Engine [cached since 148.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:49.629989', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:49.629982", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:49,704 - sqlalchemy.engine.Engine - INFO - [cached since 148.6s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:49.629989', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/contact-us.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:49.629982", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:49,769 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:49,769 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:49,911 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:49,911 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-08-03 18:03:50,138 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:50,138 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:50,139 INFO sqlalchemy.engine.Engine SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:50,139 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_url_analysis.id, entity_extraction_url_analysis.analysis_id, entity_extraction_url_analysis.entity_analysis_id, entity_extraction_url_analysis.scrape_request_ref_id, entity_extraction_url_analysis.url, entity_extraction_url_analysis.url_type, entity_extraction_url_analysis.url_classification, entity_extraction_url_analysis.url_source_table, entity_extraction_url_analysis.is_reachable_by_gemini, entity_extraction_url_analysis.soft_classification, entity_extraction_url_analysis.hard_classification, entity_extraction_url_analysis.extracted_text, entity_extraction_url_analysis.extracted_text_length, entity_extraction_url_analysis.extracted_entities, entity_extraction_url_analysis.processing_method, entity_extraction_url_analysis.processing_status, entity_extraction_url_analysis.error_message, entity_extraction_url_analysis.created_at, entity_extraction_url_analysis.processed_at, entity_extraction_url_analysis.org_id 
FROM entity_extraction_url_analysis 
WHERE entity_extraction_url_analysis.analysis_id = %(analysis_id_1)s AND entity_extraction_url_analysis.url = %(url_1)s
2025-08-03 18:03:50,139 INFO sqlalchemy.engine.Engine [cached since 2.214s ago] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/about-us/careers.html'}
2025-08-03 18:03:50,139 - sqlalchemy.engine.Engine - INFO - [cached since 2.214s ago] {'analysis_id_1': 174, 'url_1': 'https://www.shell.in/about-us/careers.html'}
[2025-08-03 18:03:50][EntityExtractor][None][************************************] WARNING: URL analysis record not found for URL: https://www.shell.in/about-us/careers.html
2025-08-03 18:03:50,263 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:50,263 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:50,263 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:50,263 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:50,263 INFO sqlalchemy.engine.Engine [cached since 149.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:50.201514', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:50.201504", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:50,263 - sqlalchemy.engine.Engine - INFO - [cached since 149.1s ago] {'analysis_id': 0, 'timestamp': '2025-08-03T18:03:50.201514', 'type': 'entity_extractor', 'messages': '{"level": "WARNING", "message": "URL analysis record not found for URL: https://www.shell.in/about-us/careers.html", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:50.201504", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:50,317 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:50,317 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:50,430 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:50,430 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:03:50][EntityExtractor][174][************************************] INFO: Gemini extraction rate 81.8% < 90%, forcing backup flow for all fields
2025-08-03 18:03:50,617 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:50,617 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:50,617 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:50,617 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:50,618 INFO sqlalchemy.engine.Engine [cached since 149.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:50.565126', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 81.8% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:50.565118", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:50,618 - sqlalchemy.engine.Engine - INFO - [cached since 149.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:50.565126', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini extraction rate 81.8% < 90%, forcing backup flow for all fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:50.565118", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:50,677 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:50,677 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:50][EntityExtractor][174][************************************] INFO: Gemini missing fields to fallback: ['shipping_countries', 'shipping_policy_details']
2025-08-03 18:03:50,853 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:50,853 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:50,853 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:50,853 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:50,853 INFO sqlalchemy.engine.Engine [cached since 149.7s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:50.782650', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:50.782640", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:50,853 - sqlalchemy.engine.Engine - INFO - [cached since 149.7s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:50.782650', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Gemini missing fields to fallback: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:50.782640", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:50,940 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:50,940 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:51][EntityExtractor][174][************************************] INFO: Starting OpenAI backup flow for missing fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 18:03:51,149 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:51,149 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:51,149 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:51,149 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:51,149 INFO sqlalchemy.engine.Engine [cached since 150s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:51.062068', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:51.062058", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:51,149 - sqlalchemy.engine.Engine - INFO - [cached since 150s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:51.062068', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Starting OpenAI backup flow for missing fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:51.062058", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:51,212 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:51,212 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:51,379 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:51,379 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:51,381 INFO sqlalchemy.engine.Engine SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 18:03:51,381 - sqlalchemy.engine.Engine - INFO - SELECT policy_analysis_new_gemini.id, policy_analysis_new_gemini.website, policy_analysis_new_gemini.scrape_request_ref_id, policy_analysis_new_gemini.analysis_flow_used, policy_analysis_new_gemini.reachability_percentage, policy_analysis_new_gemini.total_urls_processed, policy_analysis_new_gemini.privacy_policy_url, policy_analysis_new_gemini.terms_and_condition_url, policy_analysis_new_gemini.shipping_delivery_url, policy_analysis_new_gemini.returns_cancellation_exchange_url, policy_analysis_new_gemini.contact_us_url, policy_analysis_new_gemini.home_page_url, policy_analysis_new_gemini.about_us_url, policy_analysis_new_gemini.privacy_policy_text, policy_analysis_new_gemini.terms_and_condition_text, policy_analysis_new_gemini.shipping_delivery_text, policy_analysis_new_gemini.returns_cancellation_exchange_text, policy_analysis_new_gemini.contact_us_text, policy_analysis_new_gemini.home_page_text, policy_analysis_new_gemini.about_us_text, policy_analysis_new_gemini.privacy_policy_screenshot, policy_analysis_new_gemini.terms_and_condition_screenshot, policy_analysis_new_gemini.shipping_delivery_screenshot, policy_analysis_new_gemini.returns_cancellation_exchange_screenshot, policy_analysis_new_gemini.contact_us_screenshot, policy_analysis_new_gemini.home_page_screenshot, policy_analysis_new_gemini.about_us_screenshot, policy_analysis_new_gemini.instagram_url, policy_analysis_new_gemini.instagram_text, policy_analysis_new_gemini.instagram_screenshot, policy_analysis_new_gemini.youtube_url, policy_analysis_new_gemini.youtube_text, policy_analysis_new_gemini.youtube_screenshot, policy_analysis_new_gemini.facebook_url, policy_analysis_new_gemini.facebook_text, policy_analysis_new_gemini.facebook_screenshot, policy_analysis_new_gemini.twitter_url, policy_analysis_new_gemini.twitter_text, policy_analysis_new_gemini.twitter_screenshot, policy_analysis_new_gemini.linkedin_url, policy_analysis_new_gemini.linkedin_text, policy_analysis_new_gemini.linkedin_screenshot, policy_analysis_new_gemini.pinterest_url, policy_analysis_new_gemini.pinterest_text, policy_analysis_new_gemini.pinterest_screenshot, policy_analysis_new_gemini.x_url, policy_analysis_new_gemini.x_text, policy_analysis_new_gemini.x_screenshot, policy_analysis_new_gemini.result_status, policy_analysis_new_gemini.failed_at, policy_analysis_new_gemini.last_updated, policy_analysis_new_gemini.details, policy_analysis_new_gemini.processing_status, policy_analysis_new_gemini.created_at, policy_analysis_new_gemini.started_at, policy_analysis_new_gemini.completed_at, policy_analysis_new_gemini.error_message, policy_analysis_new_gemini.org_id 
FROM policy_analysis_new_gemini 
WHERE policy_analysis_new_gemini.scrape_request_ref_id = %(scrape_request_ref_id_1)s ORDER BY policy_analysis_new_gemini.id DESC
2025-08-03 18:03:51,381 INFO sqlalchemy.engine.Engine [generated in 0.00016s] {'scrape_request_ref_id_1': '************************************'}
2025-08-03 18:03:51,381 - sqlalchemy.engine.Engine - INFO - [generated in 0.00016s] {'scrape_request_ref_id_1': '************************************'}
[2025-08-03 18:03:51][EntityExtractor][174][************************************] INFO: Retrieved policy texts: ['privacy_policy', 'terms_and_condition', 'contact_us', 'about_us']
2025-08-03 18:03:51,573 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:51,573 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:51,573 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:51,573 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:51,573 INFO sqlalchemy.engine.Engine [cached since 150.4s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:51.521977', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:51.521968", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:51,573 - sqlalchemy.engine.Engine - INFO - [cached since 150.4s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:51.521977', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Retrieved policy texts: [\'privacy_policy\', \'terms_and_condition\', \'contact_us\', \'about_us\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:51.521968", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:51,635 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:51,635 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:51,789 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:03:51,789 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:03:51][EntityExtractor][174][************************************] INFO: Found policy texts for 4 policy types
2025-08-03 18:03:52,059 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:52,059 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:52,059 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:52,059 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:52,059 INFO sqlalchemy.engine.Engine [cached since 150.9s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:51.997573', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:51.997563", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:52,059 - sqlalchemy.engine.Engine - INFO - [cached since 150.9s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:51.997573', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Found policy texts for 4 policy types", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:51.997563", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:52,127 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:52,127 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:52][EntityExtractor][174][************************************] INFO: Processing shipping_info with fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 18:03:52,416 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:52,416 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:52,416 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:52,416 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:52,416 INFO sqlalchemy.engine.Engine [cached since 151.3s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:52.366656', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:52.366648", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:52,416 - sqlalchemy.engine.Engine - INFO - [cached since 151.3s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:52.366656', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Processing shipping_info with fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:52.366648", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:52,469 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:52,469 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:53,769 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 18:03:53,979 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:53,979 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:53,979 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:53,979 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:53,979 INFO sqlalchemy.engine.Engine [cached since 152.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:03:52', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:03:53,979 - sqlalchemy.engine.Engine - INFO - [cached since 152.9s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:03:52', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (2726 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:03:54,051 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:54,051 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:54][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:03:54][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_shipping_info, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:03:54][EntityExtractor][174][************************************] INFO: OpenAI shipping_info call extracted 0 fields: []
2025-08-03 18:03:54,245 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:54,245 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:54,246 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:54,246 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:54,246 INFO sqlalchemy.engine.Engine [cached since 153.1s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:54.182055', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:54.182050", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:54,246 - sqlalchemy.engine.Engine - INFO - [cached since 153.1s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:54.182055', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI shipping_info call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:54.182050", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:54,317 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:54,317 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:54][EntityExtractor][174][************************************] INFO: Making final comprehensive call for remaining fields: ['shipping_countries', 'shipping_policy_details']
2025-08-03 18:03:54,529 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:54,529 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:54,530 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:54,530 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:54,530 INFO sqlalchemy.engine.Engine [cached since 153.4s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:54.455894', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:54.455887", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:54,530 - sqlalchemy.engine.Engine - INFO - [cached since 153.4s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:54.455894', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Making final comprehensive call for remaining fields: [\'shipping_countries\', \'shipping_policy_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:54.455887", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:54,585 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:54,585 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:56,244 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-03 18:03:56,315 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:56,315 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:56,316 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:56,316 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:56,316 INFO sqlalchemy.engine.Engine [cached since 155.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:03:54', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:03:56,316 - sqlalchemy.engine.Engine - INFO - [cached since 155.2s ago] {'analysis_id': 0, 'timestamp': '2025-08-03 18:03:54', 'type': 'openai', 'messages': '[\n    {\n        "role": "user",\n        "content": "\\nYou are an expert at extracting business information from policy documents and website cont ... (14443 characters truncated) ... \\"shipping_policy_details\\": \\"extracted shipping policy details or null\\"\\n}\\n\\nReturn only the JSON response, no additional text."\n    }\n]', 'response': '"{\\n    \\"shipping_countries\\": \\"null\\",\\n    \\"shipping_policy_details\\": \\"null\\"\\n}"', 'org_id': 'default'}
2025-08-03 18:03:56,454 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:56,454 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:56][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: None
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:03:56][api_response_logger][unknown] INFO: OpenAI API Call - Name: entity_extraction_comprehensive, Request ID: ************************************
{
  "response_preview": "{\n    \"shipping_countries\": \"null\",\n    \"shipping_policy_details\": \"null\"\n}"
}
[2025-08-03 18:03:56][EntityExtractor][174][************************************] INFO: OpenAI comprehensive call extracted 0 fields: []
2025-08-03 18:03:56,642 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:56,642 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:56,643 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:56,643 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:56,643 INFO sqlalchemy.engine.Engine [cached since 155.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:56.587950', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:56.587945", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:56,643 - sqlalchemy.engine.Engine - INFO - [cached since 155.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:56.587950', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI comprehensive call extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:56.587945", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:56,705 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:56,705 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:56][EntityExtractor][174][************************************] INFO: Total OpenAI backup extracted 0 fields: []
2025-08-03 18:03:56,866 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:56,866 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:56,867 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:56,867 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:56,867 INFO sqlalchemy.engine.Engine [cached since 155.7s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:56.805005', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:56.804998", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:56,867 - sqlalchemy.engine.Engine - INFO - [cached since 155.7s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:56.805005', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Total OpenAI backup extracted 0 fields: []", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:56.804998", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:56,919 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:56,919 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:57][EntityExtractor][174][************************************] INFO: OpenAI backup completed but no additional data extracted
2025-08-03 18:03:57,115 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:57,115 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:57,116 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,116 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,116 INFO sqlalchemy.engine.Engine [cached since 156s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.049281', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup completed but no additional data extracted", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.049273", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,116 - sqlalchemy.engine.Engine - INFO - [cached since 156s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.049281', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "OpenAI backup completed but no additional data extracted", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.049273", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,168 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:57,168 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:57][EntityExtractor][174][************************************] INFO: Merging 1 AI extraction results
2025-08-03 18:03:57,375 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:57,375 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:57,376 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,376 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,376 INFO sqlalchemy.engine.Engine [cached since 156.3s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.324154', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 1 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.324146", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,376 - sqlalchemy.engine.Engine - INFO - [cached since 156.3s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.324154', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merging 1 AI extraction results", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.324146", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,455 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:57,455 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:57][EntityExtractor][174][************************************] INFO: Merged result contains 8 fields
2025-08-03 18:03:57,649 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:57,649 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:57,649 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,649 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,649 INFO sqlalchemy.engine.Engine [cached since 156.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.596745', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.596739", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,649 - sqlalchemy.engine.Engine - INFO - [cached since 156.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.596745', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Merged result contains 8 fields", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.596739", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,711 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:57,711 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:57][EntityExtractor][174][************************************] INFO: Final merged extraction result keys: ['legal_name', 'business_email', 'support_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details']
2025-08-03 18:03:57,886 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:57,886 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:57,886 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,886 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:57,886 INFO sqlalchemy.engine.Engine [cached since 156.8s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.837966', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_numbers ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.837958", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,886 - sqlalchemy.engine.Engine - INFO - [cached since 156.8s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:57.837966', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Final merged extraction result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_numbers ... (75 characters truncated) ... , \'jurisdiction_details\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:57.837958", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:57,939 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:57,939 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:58][EntityExtractor][174][************************************] INFO: Fallback raw text for privacy_policy_text: present
2025-08-03 18:03:58,111 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:58,111 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:58,112 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,112 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,112 INFO sqlalchemy.engine.Engine [cached since 157s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.057211', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.057201", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,112 - sqlalchemy.engine.Engine - INFO - [cached since 157s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.057211', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for privacy_policy_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.057201", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,172 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:58,172 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:58][EntityExtractor][174][************************************] INFO: After fallback, merged_result[privacy_policy_text] length: 2340
2025-08-03 18:03:58,337 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:58,337 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:58,338 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,338 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,338 INFO sqlalchemy.engine.Engine [cached since 157.2s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.273014', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.273003", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,338 - sqlalchemy.engine.Engine - INFO - [cached since 157.2s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.273014', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[privacy_policy_text] length: 2340", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.273003", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,405 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:58,405 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:58][EntityExtractor][174][************************************] INFO: Fallback raw text for terms_conditions_text: present
2025-08-03 18:03:58,583 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:58,583 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:58,583 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,583 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,583 INFO sqlalchemy.engine.Engine [cached since 157.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.530402', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.530393", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,583 - sqlalchemy.engine.Engine - INFO - [cached since 157.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.530402', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Fallback raw text for terms_conditions_text: present", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.530393", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,647 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:58,647 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:58][EntityExtractor][174][************************************] INFO: After fallback, merged_result[terms_conditions_text] length: 1479
2025-08-03 18:03:58,845 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:58,845 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:58,845 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,845 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:58,845 INFO sqlalchemy.engine.Engine [cached since 157.7s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.790936', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.790929", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,845 - sqlalchemy.engine.Engine - INFO - [cached since 157.7s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:58.790936', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "After fallback, merged_result[terms_conditions_text] length: 1479", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:58.790929", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:58,914 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:58,914 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:59][EntityExtractor][174][************************************] INFO: Before database storage - jurisdiction_details: Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]
2025-08-03 18:03:59,083 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:59,083 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:59,083 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:59,083 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:59,083 INFO sqlalchemy.engine.Engine [cached since 158s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:59.033157', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:59.033150", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:59,083 - sqlalchemy.engine.Engine - INFO - [cached since 158s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:59.033157', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_details: Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:59.033150", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:59,147 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:59,147 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:59][EntityExtractor][174][************************************] INFO: Before database storage - jurisdiction_place: ['India']
2025-08-03 18:03:59,331 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:59,331 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:59,331 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:59,331 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:59,331 INFO sqlalchemy.engine.Engine [cached since 158.2s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:59.251962', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:59.251954", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:59,331 - sqlalchemy.engine.Engine - INFO - [cached since 158.2s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:59.251962', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:59.251954", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:59,401 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:59,401 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:03:59][EntityExtractor][174][************************************] INFO: Before database storage - has_jurisdiction_law: True
2025-08-03 18:03:59,604 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:59,604 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:59,604 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:59,604 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:03:59,604 INFO sqlalchemy.engine.Engine [cached since 158.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:59.535872', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - has_jurisdiction_law: True", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:59.535864", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:59,604 - sqlalchemy.engine.Engine - INFO - [cached since 158.5s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:03:59.535872', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Before database storage - has_jurisdiction_law: True", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:03:59.535864", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:03:59,709 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:59,709 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:03:59,903 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:03:59,903 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:03:59,905 INFO sqlalchemy.engine.Engine INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 18:03:59,905 - sqlalchemy.engine.Engine - INFO - INSERT INTO entity_extraction_analysis (scrape_request_ref_id, website_url, processing_status, legal_name, business_email, support_email, business_contact_numbers, business_location, has_jurisdiction_law, jurisdiction_details, accepts_international_orders, shipping_policy_details, jurisdiction_place, shipping_countries, privacy_policy_text, terms_conditions_text, urls_reachable_by_gemini, urls_not_reachable_by_gemini, extraction_method, total_urls_processed, all_urls_found, reachable_urls, unreachable_urls, policy_urls_matched, created_at, started_at, completed_at, error_message, org_id) VALUES (%(scrape_request_ref_id)s, %(website_url)s, %(processing_status)s, %(legal_name)s, %(business_email)s, %(support_email)s, %(business_contact_numbers)s, %(business_location)s, %(has_jurisdiction_law)s, %(jurisdiction_details)s, %(accepts_international_orders)s, %(shipping_policy_details)s, %(jurisdiction_place)s, %(shipping_countries)s, %(privacy_policy_text)s, %(terms_conditions_text)s, %(urls_reachable_by_gemini)s, %(urls_not_reachable_by_gemini)s, %(extraction_method)s, %(total_urls_processed)s, %(all_urls_found)s, %(reachable_urls)s, %(unreachable_urls)s, %(policy_urls_matched)s, %(created_at)s, %(started_at)s, %(completed_at)s, %(error_message)s, %(org_id)s)
2025-08-03 18:03:59,905 INFO sqlalchemy.engine.Engine [generated in 0.00029s] {'scrape_request_ref_id': '************************************', 'website_url': 'https://www.shell.in', 'processing_status': 'COMPLETED', 'legal_name': 'Shell India Markets Private Limited', 'business_email': '<EMAIL>', 'support_email': '<EMAIL>', 'business_contact_numbers': '044-3099 1103, 044-4344 2650, +91 44 46945101', 'business_location': 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'has_jurisdiction_law': True, 'jurisdiction_details': 'Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]', 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': 'India', 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': '["https://www.shell.in/privacy.html", "https://www.shell.in/terms-and-conditions.html", "not_found", "https://www.shell.in/about-us/contact-us.html", "https://www.shell.in/about-us/careers.html"]', 'urls_not_reachable_by_gemini': None, 'extraction_method': 'mixed', 'total_urls_processed': 5, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T18:03:59.826743', 'started_at': '2025-08-03T18:03:59.826731', 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 18:03:59,905 - sqlalchemy.engine.Engine - INFO - [generated in 0.00029s] {'scrape_request_ref_id': '************************************', 'website_url': 'https://www.shell.in', 'processing_status': 'COMPLETED', 'legal_name': 'Shell India Markets Private Limited', 'business_email': '<EMAIL>', 'support_email': '<EMAIL>', 'business_contact_numbers': '044-3099 1103, 044-4344 2650, +91 44 46945101', 'business_location': 'Commerzone, Block-2, No.2, 200 Feet Radial Road, Pallikaranai, Chennai – 600100 India', 'has_jurisdiction_law': True, 'jurisdiction_details': 'Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]', 'accepts_international_orders': None, 'shipping_policy_details': None, 'jurisdiction_place': 'India', 'shipping_countries': None, 'privacy_policy_text': None, 'terms_conditions_text': None, 'urls_reachable_by_gemini': '["https://www.shell.in/privacy.html", "https://www.shell.in/terms-and-conditions.html", "not_found", "https://www.shell.in/about-us/contact-us.html", "https://www.shell.in/about-us/careers.html"]', 'urls_not_reachable_by_gemini': None, 'extraction_method': 'mixed', 'total_urls_processed': 5, 'all_urls_found': None, 'reachable_urls': None, 'unreachable_urls': None, 'policy_urls_matched': None, 'created_at': '2025-08-03T18:03:59.826743', 'started_at': '2025-08-03T18:03:59.826731', 'completed_at': None, 'error_message': None, 'org_id': '2'}
2025-08-03 18:03:59,994 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:03:59,994 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:04:00,200 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:04:00,200 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:04:00,201 INFO sqlalchemy.engine.Engine SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 18:04:00,201 - sqlalchemy.engine.Engine - INFO - SELECT entity_extraction_analysis.id, entity_extraction_analysis.scrape_request_ref_id, entity_extraction_analysis.website_url, entity_extraction_analysis.processing_status, entity_extraction_analysis.legal_name, entity_extraction_analysis.business_email, entity_extraction_analysis.support_email, entity_extraction_analysis.business_contact_numbers, entity_extraction_analysis.business_location, entity_extraction_analysis.has_jurisdiction_law, entity_extraction_analysis.jurisdiction_details, entity_extraction_analysis.accepts_international_orders, entity_extraction_analysis.shipping_policy_details, entity_extraction_analysis.jurisdiction_place, entity_extraction_analysis.shipping_countries, entity_extraction_analysis.privacy_policy_text, entity_extraction_analysis.terms_conditions_text, entity_extraction_analysis.urls_reachable_by_gemini, entity_extraction_analysis.urls_not_reachable_by_gemini, entity_extraction_analysis.extraction_method, entity_extraction_analysis.total_urls_processed, entity_extraction_analysis.all_urls_found, entity_extraction_analysis.reachable_urls, entity_extraction_analysis.unreachable_urls, entity_extraction_analysis.policy_urls_matched, entity_extraction_analysis.created_at, entity_extraction_analysis.started_at, entity_extraction_analysis.completed_at, entity_extraction_analysis.error_message, entity_extraction_analysis.org_id 
FROM entity_extraction_analysis 
WHERE entity_extraction_analysis.id = %(pk_1)s
2025-08-03 18:04:00,202 INFO sqlalchemy.engine.Engine [generated in 0.00042s] {'pk_1': 175}
2025-08-03 18:04:00,202 - sqlalchemy.engine.Engine - INFO - [generated in 0.00042s] {'pk_1': 175}
[2025-08-03 18:04:00][EntityExtractor][174][************************************] INFO: Stored entity extraction analysis with ID: 175
2025-08-03 18:04:00,329 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:04:00,329 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:04:00,330 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:00,330 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:00,330 INFO sqlalchemy.engine.Engine [cached since 159.2s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:00.268420', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored entity extraction analysis with ID: 175", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:00.268411", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:00,330 - sqlalchemy.engine.Engine - INFO - [cached since 159.2s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:00.268420', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Stored entity extraction analysis with ID: 175", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:00.268411", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:00,402 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:04:00,402 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-08-03 18:04:00,546 INFO sqlalchemy.engine.Engine ROLLBACK
2025-08-03 18:04:00,546 - sqlalchemy.engine.Engine - INFO - ROLLBACK
[2025-08-03 18:04:00][EntityExtractor][174][************************************] INFO: Creating response with merged_result keys: ['legal_name', 'business_email', 'support_email', 'business_contact_numbers', 'business_location', 'has_jurisdiction_law', 'jurisdiction_place', 'jurisdiction_details', 'privacy_policy_text', 'terms_conditions_text']
2025-08-03 18:04:00,723 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:04:00,723 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:04:00,723 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:00,723 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:00,723 INFO sqlalchemy.engine.Engine [cached since 159.6s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:00.650333', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_n ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:00.650326", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:00,723 - sqlalchemy.engine.Engine - INFO - [cached since 159.6s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:00.650333', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Creating response with merged_result keys: [\'legal_name\', \'business_email\', \'support_email\', \'business_contact_n ... (133 characters truncated) ...  \'terms_conditions_text\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:00.650326", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:00,799 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:04:00,799 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:04:00][EntityExtractor][174][************************************] INFO: Response jurisdiction_details: Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]
2025-08-03 18:04:01,030 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:04:01,030 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:04:01,030 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:01,030 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:01,030 INFO sqlalchemy.engine.Engine [cached since 159.9s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:00.947834', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:00.947826", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:01,030 - sqlalchemy.engine.Engine - INFO - [cached since 159.9s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:00.947834', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_details: Website owned and operated by Shell India Markets Private Limited, an Indian company. [2]", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:00.947826", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:01,110 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:04:01,110 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:04:01][EntityExtractor][174][************************************] INFO: Response jurisdiction_place: ['India']
2025-08-03 18:04:01,271 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:04:01,271 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:04:01,271 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:01,271 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:01,271 INFO sqlalchemy.engine.Engine [cached since 160.1s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:01.212324', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:01.212315", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:01,271 - sqlalchemy.engine.Engine - INFO - [cached since 160.1s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:01.212324', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response jurisdiction_place: [\'India\']", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:01.212315", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:01,357 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:04:01,357 - sqlalchemy.engine.Engine - INFO - COMMIT
[2025-08-03 18:04:01][EntityExtractor][174][************************************] INFO: Response has_jurisdiction_law: yes
2025-08-03 18:04:01,550 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-08-03 18:04:01,550 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-08-03 18:04:01,550 INFO sqlalchemy.engine.Engine INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:01,550 - sqlalchemy.engine.Engine - INFO - INSERT INTO general_logs_gemini (analysis_id, timestamp, type, messages, response, org_id) VALUES (%(analysis_id)s, %(timestamp)s, %(type)s, %(messages)s, %(response)s, %(org_id)s)
2025-08-03 18:04:01,550 INFO sqlalchemy.engine.Engine [cached since 160.4s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:01.485245', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:01.485237", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:01,550 - sqlalchemy.engine.Engine - INFO - [cached since 160.4s ago] {'analysis_id': 174, 'timestamp': '2025-08-03T18:04:01.485245', 'type': 'entity_extractor', 'messages': '{"level": "INFO", "message": "Response has_jurisdiction_law: yes", "scrape_request_ref_id": "************************************", "timestamp": "2025-08-03T18:04:01.485237", "data": {}}', 'response': '', 'org_id': 'default'}
2025-08-03 18:04:01,684 INFO sqlalchemy.engine.Engine COMMIT
2025-08-03 18:04:01,684 - sqlalchemy.engine.Engine - INFO - COMMIT
INFO:     127.0.0.1:57070 - "POST /entity-extraction/analyze HTTP/1.1" 200 OK
